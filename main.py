import os
import json
import base64
from fastapi import FastAP<PERSON>, Request
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from starlette.middleware.sessions import SessionMiddleware
from authlib.integrations.starlette_client import OAuth
from sqlalchemy import create_engine, text
from dotenv import load_dotenv
from icecream import ic


# Carica le variabili d'ambiente dal file .env
load_dotenv()

# --- Configurazione ---

# App FastAPI
app = FastAPI()

# Aggiungi il middleware per la gestione delle sessioni
SECRET_KEY = os.getenv("SECRET_KEY")
if not SECRET_KEY:
    raise ValueError("SECRET_KEY non impostata nel file .env. Generane una con 'openssl rand -hex 32'")
app.add_middleware(
    SessionMiddleware,
    secret_key=SECRET_KEY,
    same_site='lax',  # Rilassiamo la policy, dato che HTTPS non ha risolto
    https_only=False
)

# Template Jinja2
templates = Jinja2Templates(directory="templates")

# Configurazione OAuth per WSO2 con Authlib
WSO2_CLIENT_ID = os.getenv('WSO2_CLIENT_ID')
if not WSO2_CLIENT_ID:
    raise ValueError("WSO2_CLIENT_ID not set in the .env file")

WSO2_CLIENT_SECRET = os.getenv('WSO2_CLIENT_SECRET')
if not WSO2_CLIENT_SECRET:
    raise ValueError("WSO2_CLIENT_SECRET not set in the .env file")

WSO2_SERVER_METADATA_URL = os.getenv('WSO2_SERVER_METADATA_URL')
if not WSO2_SERVER_METADATA_URL:
    raise ValueError("WSO2_SERVER_METADATA_URL not set in the .env file")

oauth = OAuth()
oauth.register(
    name='wso2',
    client_id=WSO2_CLIENT_ID,
    client_secret=WSO2_CLIENT_SECRET,
    server_metadata_url=WSO2_SERVER_METADATA_URL,
    client_kwargs={
        'scope': 'openid email profile',
    }
)

# Configurazione Database con SQLAlchemy
DATABASE_URL = os.getenv("DATABASE_URL")
if not DATABASE_URL:
    raise ValueError("DATABASE_URL non impostata nel file .env")
engine = create_engine(DATABASE_URL)


# --- Funzioni di Utilità ---

def decode_jwt_payload(jwt_token):
    """Decodifica il payload di un JWT senza verificare la firma."""
    try:
        # Un JWT è composto da tre parti separate da punti: header.payload.signature
        parts = jwt_token.split('.')
        if len(parts) != 3:
            raise ValueError("Invalid JWT format")

        # Decodifica il payload (seconda parte)
        payload = parts[1]

        # Aggiungi padding se necessario per base64
        padding = len(payload) % 4
        if padding:
            payload += '=' * (4 - padding)

        # Decodifica da base64
        decoded_bytes = base64.urlsafe_b64decode(payload)

        # Converte in JSON
        return json.loads(decoded_bytes.decode('utf-8'))
    except Exception as e:
        raise ValueError(f"Failed to decode JWT payload: {e}")

def get_db_connection_status():
    """Verifica la connessione al database e restituisce lo stato."""
    try:
        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
        return "Connected successfully to the database."
    except Exception as e:
        return f"Error connecting to the database: {e}"


# --- Endpoint dell'Applicazione ---

@app.get("/", response_class=HTMLResponse)
async def homepage(request: Request):
    user = request.session.get('user')
    if user:
        return RedirectResponse(url='/dashboard')
    return templates.TemplateResponse("login.html", {"request": request})


@app.get("/login")
async def login(request: Request):
    redirect_uri = request.url_for('auth')
    wso2_client = oauth.create_client('wso2')
    if not wso2_client:
        return HTMLResponse('<h1>OAuth Configuration Error</h1><p>WSO2 client not configured properly</p>', status_code=500)
    return await wso2_client.authorize_redirect(request, redirect_uri)


@app.get("/auth")
async def auth(request: Request):
    """OAuth callback endpoint - gestisce l'autenticazione e crea la sessione utente."""
    try:
        wso2_client = oauth.create_client('wso2')
        if not wso2_client:
            return HTMLResponse('<h1>OAuth Configuration Error</h1><p>WSO2 client not configured properly</p>', status_code=500)

        # Ottieni il token di accesso
        token = await wso2_client.authorize_access_token(request)

        # Decodifica l'ID token senza validazione del nonce per ottenere le informazioni utente
        # Questo è accettabile perché abbiamo già validato il token durante authorize_access_token
        user_info = None

        if 'id_token' in token:
            try:
                # Decodifica l'ID token senza verifica della firma
                user_info = decode_jwt_payload(token['id_token'])
                ic(user_info)
            except Exception as jwt_error:
                return HTMLResponse(f'<h1>Token Decode Error</h1><p>{jwt_error}</p>', status_code=400)

        if user_info:
            # Estrai solo i dati essenziali per ridurre la dimensione del cookie
            essential_user_data = {
                'sub': user_info.get('sub'),
                'email': user_info.get('http://wso2.org/claims/emailaddress'),
                'username': user_info.get('http://wso2.org/claims/username'),
                'given_name': user_info.get('http://wso2.org/claims/givenname'),
                'family_name': user_info.get('http://wso2.org/claims/lastname'),
                'display_name': user_info.get('http://wso2.org/claims/displayName'),
                'full_name': user_info.get('http://wso2.org/claims/fullname'),
                # Salva solo i ruoli più importanti per determinare il profilo
                'is_super_admin': 'INTERNAL/IDUMANAGER_SuperAdmin' in user_info.get('http://wso2.org/claims/role', []),
                'is_sonnino_admin': 'G_Admins_Sonnino' in user_info.get('http://wso2.org/claims/role', []),
                # Mantieni alcuni ruoli chiave se necessari
                'key_roles': [role for role in user_info.get('http://wso2.org/claims/role', [])
                             if role in ['INTERNAL/IDUMANAGER_SuperAdmin', 'G_Admins_Sonnino', 'INTERNAL/IDUMANAGER_Admin']]
            }

            # Debug: log della sessione prima del salvataggio
            print(f"[DEBUG] Session before save: {list(request.session.keys())}")
            print(f"[DEBUG] Essential data size: {len(str(essential_user_data))} chars")

            # Pulisci la sessione da eventuali stati OAuth precedenti
            keys_to_remove = [key for key in request.session.keys() if key.startswith('_state_wso2_')]
            for key in keys_to_remove:
                del request.session[key]

            # Salva solo i dati essenziali (non l'intero ID token)
            request.session['user'] = essential_user_data
            # Salva solo una versione ridotta dell'ID token se necessario per il logout
            request.session['has_id_token'] = bool(token.get('id_token'))

            # La sessione viene automaticamente salvata quando modificata

            # Debug: log della sessione dopo il salvataggio
            print(f"[DEBUG] Session after save: {list(request.session.keys())}")
            print(f"[DEBUG] User saved: {essential_user_data.get('sub', 'unknown')}")

            # Reindirizza alla dashboard
            return RedirectResponse(url='/dashboard', status_code=303)
        else:
            return HTMLResponse('<h1>Impossibile elaborare il token utente</h1>', status_code=400)

    except Exception as e:
        return HTMLResponse(f'<h1>Authentication Error</h1><p>{e}</p>', status_code=400)





@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request):
    # Debug: log della sessione all'arrivo
    print(f"[DEBUG] /dashboard - Session keys: {list(request.session.keys())}")

    user = request.session.get('user')
    ic(user)
    print(f"[DEBUG] /dashboard - User found: {'Yes' if user else 'No'}")
    if not user:
        print("[DEBUG] /dashboard - No user, redirecting to /")
        return RedirectResponse(url='/')

    db_status = get_db_connection_status()

    # Usa la nuova struttura dati semplificata
    full_name_parts = user.get('full_name', [])
    if isinstance(full_name_parts, list):
        full_name = ' '.join(part.strip() for part in full_name_parts if part and part.strip())
    else:
        full_name = str(full_name_parts) if full_name_parts else ''

    if not full_name:
        full_name = user.get('display_name', user.get('given_name', user.get('sub', 'Unknown User')))

    # Usa i flag booleani per determinare il profilo
    profile = 'guest'
    if user.get('is_super_admin') or user.get('is_sonnino_admin'):
        profile = 'editor'

    context = {
        "request": request,
        "user": user,
        "db_status": db_status,
        "full_name": full_name,
        "profile": profile
    }
    return templates.TemplateResponse("dashboard.html", context)


@app.get("/logout")
async def logout(request: Request):
    # Con la nuova struttura, non salviamo più l'ID token completo
    # ma possiamo comunque fare il logout locale
    request.session.clear()

    # Semplice logout locale - reindirizza alla homepage
    # Per un logout completo da WSO2, sarebbe necessario l'ID token completo
    # ma per ridurre la dimensione del cookie, facciamo solo logout locale
    return RedirectResponse(url='/')
