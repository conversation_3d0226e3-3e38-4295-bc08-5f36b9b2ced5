import os
import json
from fastapi import FastAP<PERSON>, Request, Query
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from starlette.middleware.sessions import SessionMiddleware
from authlib.integrations.starlette_client import OAuth
from sqlalchemy import create_engine, text
from dotenv import load_dotenv
from icecream import ic
from urllib.parse import urlencode

# Carica le variabili d'ambiente dal file .env
load_dotenv()

# --- Configurazione ---

# App FastAPI
app = FastAPI()

# Aggiungi il middleware per la gestione delle sessioni
SECRET_KEY = os.getenv("SECRET_KEY")
if not SECRET_KEY:
    raise ValueError("SECRET_KEY non impostata nel file .env. Generane una con 'openssl rand -hex 32'")
app.add_middleware(
    SessionMiddleware,
    secret_key=SECRET_KEY,
    same_site='lax',  # Rilassiamo la policy, dato che HTTPS non ha risolto
    https_only=False
)

# Template Jinja2
templates = Jinja2Templates(directory="templates")

# Configurazione OAuth per WSO2 con Authlib
WSO2_CLIENT_ID = os.getenv('WSO2_CLIENT_ID')
if not WSO2_CLIENT_ID:
    raise ValueError("WSO2_CLIENT_ID not set in the .env file")

WSO2_CLIENT_SECRET = os.getenv('WSO2_CLIENT_SECRET')
if not WSO2_CLIENT_SECRET:
    raise ValueError("WSO2_CLIENT_SECRET not set in the .env file")

WSO2_SERVER_METADATA_URL = os.getenv('WSO2_SERVER_METADATA_URL')
if not WSO2_SERVER_METADATA_URL:
    raise ValueError("WSO2_SERVER_METADATA_URL not set in the .env file")

oauth = OAuth()
oauth.register(
    name='wso2',
    client_id=WSO2_CLIENT_ID,
    client_secret=WSO2_CLIENT_SECRET,
    server_metadata_url=WSO2_SERVER_METADATA_URL,
    client_kwargs={
        'scope': 'openid email profile',
    }
)

# Configurazione Database con SQLAlchemy
DATABASE_URL = os.getenv("DATABASE_URL")
if not DATABASE_URL:
    raise ValueError("DATABASE_URL non impostata nel file .env")
engine = create_engine(DATABASE_URL)


# --- Funzioni di Utilità ---

def get_db_connection_status():
    """Verifica la connessione al database e restituisce lo stato."""
    try:
        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
        return "Connected successfully to the database."
    except Exception as e:
        return f"Error connecting to the database: {e}"


# --- Endpoint dell'Applicazione ---

@app.get("/", response_class=HTMLResponse)
async def homepage(request: Request):
    user = request.session.get('user')
    if user:
        return RedirectResponse(url='/dashboard')
    return templates.TemplateResponse("login.html", {"request": request})


@app.get("/login")
async def login(request: Request):
    redirect_uri = request.url_for('auth')
    wso2_client = oauth.create_client('wso2')
    if not wso2_client:
        return HTMLResponse('<h1>OAuth Configuration Error</h1><p>WSO2 client not configured properly</p>', status_code=500)
    return await wso2_client.authorize_redirect(request, redirect_uri)


@app.get("/auth")
async def auth(request: Request):
    """Step 1: Recupera il token da WSO2 e lo passa al processore interno."""
    try:
        wso2_client = oauth.create_client('wso2')
        if not wso2_client:
            return HTMLResponse('<h1>OAuth Configuration Error</h1><p>WSO2 client not configured properly</p>', status_code=500)
        token = await wso2_client.authorize_access_token(request)
        # Non toccare la sessione qui. Passa i token al prossimo step.
        return RedirectResponse(
            url=f"/process-token?access_token={token['access_token']}&id_token={token['id_token']}"
        )
    except Exception as e:
        return HTMLResponse(f'<h1>Authentication Error</h1><p>{e}</p>', status_code=400)


@app.get("/process-token")
async def process_token(request: Request, access_token: str = Query(...), id_token: str = Query(...)):
    """Step 2: Crea la sessione utente partendo dai token ricevuti."""
    try:
        wso2_client = oauth.create_client('wso2')
        if not wso2_client:
            return HTMLResponse('<h1>OAuth Configuration Error</h1><p>WSO2 client not configured properly</p>', status_code=500)
        # Authlib richiede un oggetto token completo, lo ricostruiamo
        token = {
            'access_token': access_token,
            'id_token': id_token,
            'token_type': 'Bearer'
        }
        # Non serve il nonce qui perché non stiamo validando la richiesta di callback
        user_info = await wso2_client.parse_id_token(token)

        if user_info:
            request.session.clear() # Pulisci per sicurezza
            serializable_user_info = json.loads(json.dumps(user_info))
            request.session['user'] = serializable_user_info
            request.session['id_token'] = id_token
        else:
            return HTMLResponse('<h1>Impossibile elaborare il token utente</h1>', status_code=400)

    except Exception as e:
        return HTMLResponse(f'<h1>Token Processing Error</h1><p>{e}</p>', status_code=400)

    return RedirectResponse(url='/dashboard', status_code=303)


@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request):
    user = request.session.get('user')
    if not user:
        return RedirectResponse(url='/')

    db_status = get_db_connection_status()

    fullname_claim = 'http://wso2.org/claims/fullname'
    displayName_claim = 'http://wso2.org/claims/displayName'
    full_name_parts = user.get(fullname_claim, [])
    full_name = ' '.join(part.strip() for part in full_name_parts if part and part.strip())
    if not full_name:
        full_name = user.get(displayName_claim, user.get('sub', 'Unknown User'))

    role_claim = 'http://wso2.org/claims/role'
    roles = user.get(role_claim, [])
    profile = 'guest'
    if 'INTERNAL/IDUMANAGER_SuperAdmin' in roles:
        profile = 'admin'
    elif 'G_Admins_Sonnino' in roles:
        profile = 'editor'

    context = {
        "request": request,
        "user": user,
        "db_status": db_status,
        "full_name": full_name,
        "profile": profile
    }
    return templates.TemplateResponse("dashboard.html", context)


@app.get("/logout")
async def logout(request: Request):
    id_token = request.session.get('id_token')
    request.session.clear()

    wso2_client = oauth.create_client('wso2')
    if not wso2_client:
        return RedirectResponse(url='/')

    metadata = await wso2_client.load_server_metadata()
    logout_endpoint = metadata.get('end_session_endpoint')

    if id_token and logout_endpoint:
        post_logout_redirect_uri = request.url_for('homepage')
        params = {
            'id_token_hint': id_token,
            'post_logout_redirect_uri': str(post_logout_redirect_uri)
        }
        logout_redirect_url = f"{logout_endpoint}?{urlencode(params)}"
        return RedirectResponse(url=logout_redirect_url)

    return RedirectResponse(url='/')
