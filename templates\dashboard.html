<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; background-color: #f0f2f5; margin: 0; padding: 20px; }
        .dashboard {
            max-width: 800px;
            margin: 40px auto;
            padding: 20px 40px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        h1 { color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        .user-info, .db-status { margin-top: 20px; }
        .user-info h2, .db-status h2 { color: #555; font-size: 1.2em; }
        pre { background-color: #eef; padding: 15px; border-radius: 5px; white-space: pre-wrap; word-wrap: break-word; }
        .status-ok { color: #28a745; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        .logout-btn { display: inline-block; margin-top: 30px; padding: 10px 20px; background-color: #dc3545; color: white; text-decoration: none; border-radius: 5px; transition: background-color 0.3s; }
        .logout-btn:hover { background-color: #c82333; }
        .claims-table { width: 100%; border-collapse: collapse; margin-top: 20px; font-size: 0.9em; }
        .claims-table th, .claims-table td { border: 1px solid #ddd; padding: 8px; text-align: left; vertical-align: top; word-break: break-all; }
        .claims-table th { background-color: #f2f2f2; font-weight: bold; }
        .claims-table td ul { margin: 0; padding-left: 20px; }
    </style>
</head>
<body>
    <div class="dashboard">
        <h1>Welcome to your Dashboard</h1>

        {% if user %}
        <div class="user-info">
            <h2>User Information</h2>
            <p>Hello, <strong>{{ full_name }}</strong>!</p>
            <p>Your Profile: <strong>{{ profile }}</strong></p>
            <table class="claims-table">
                <thead>
                    <tr>
                        <th>Claim</th>
                        <th>Value</th>
                    </tr>
                </thead>
                <tbody>
                    {% for key, value in user|dictsort %}
                    <tr>
                        <td>{{ key }}</td>
                        <td>
                            {% if value is iterable and value is not string %}
                                <ul>
                                {% for item in (value|sort if key == 'http://wso2.org/claims/role' else value) %}
                                    <li>{{ item }}</li>
                                {% endfor %}
                                </ul>
                            {% else %}
                                {{ value }}
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}

        <div class="db-status">
            <h2>Database Connection Status</h2>
            <p class="{{ 'status-ok' if 'Connected' in db_status else 'status-error' }}">
                {{ db_status }}
            </p>
        </div>

        <a href="/logout" class="logout-btn">Logout</a>
    </div>
</body>
</html>
