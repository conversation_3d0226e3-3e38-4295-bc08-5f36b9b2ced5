[project]
name = "fastapi-wso2-auth"
version = "0.1.0"
description = "A FastAPI application with WSO2 OAuth2 authentication."
authors = [{ name = "<PERSON>", email = "<EMAIL>" }]
requires-python = ">=3.13"
dependencies = [
    "fastapi",
    "uvicorn[standard]",
    "sqlalchemy",
    "psycopg2-binary",
    "requests",
    "authlib",
    "jinja2",
    "itsdangerous>=2.2.0",
    "httpx>=0.28.1",
    "icecream>=2.1.8",
    "pyjwt>=2.10.1",
    "python-dotenv>=1.1.1",
]
