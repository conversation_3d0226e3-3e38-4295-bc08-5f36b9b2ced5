---
description: Repository Information Overview
alwaysApply: true
---

# FastAPI WSO2 Authentication Boilerplate Information

## Summary

A FastAPI application that demonstrates user authentication through a WSO2 IAM server using OAuth 2.0 protocol. Once authenticated, users are redirected to a welcome dashboard displaying their profile information and PostgreSQL database connection status.

## Structure

- **main.py**: Main application entry point with FastAPI routes and OAuth configuration
- **templates/**: HTML templates for the web interface
  - **login.html**: Login page with WSO2 authentication button
  - **dashboard.html**: User dashboard showing profile information and database status
- **pyproject.toml**: Project configuration and dependencies
- **.env**: Environment variables for configuration (not committed to repository)
- **cert.pem/key.pem**: SSL certificates for local HTTPS development

## Language & Runtime

**Language**: Python
**Version**: 3.13
**Build System**: uv
**Package Manager**: uv

## Dependencies

**Main Dependencies**:

- fastapi: Web framework for building APIs
- uvicorn[standard]: ASGI server for FastAPI
- sqlalchemy: SQL toolkit and ORM
- psycopg2-binary: PostgreSQL adapter
- authlib: OAuth and OpenID Connect library
- python-dotenv: Environment variable management
- jinja2: Template engine
- httpx: HTTP client
- icecream: Debugging utility

## Build & Installation

```bash
# Install dependencies
uv pip install .

# Alternative installation methods
uv pip install -r requirements.txt
# or
uv add fastapi uvicorn[standard] sqlalchemy psycopg2-binary python-dotenv requests authlib jinja2 itsdangerous httpx
```

## Usage & Operations

```bash
# Start the development server
uv run uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## Configuration

**Environment Variables**:

- WSO2_CLIENT_ID: OAuth client ID from WSO2
- WSO2_CLIENT_SECRET: OAuth client secret from WSO2
- WSO2_SERVER_METADATA_URL: OpenID Connect discovery URL
- DATABASE_URL: PostgreSQL connection string
- SECRET_KEY: Secret key for session encryption

## Application Flow

1. **Login Page**: Users visit the homepage and see a login button
2. **WSO2 Authentication**: Clicking the button redirects to WSO2 login page
3. **Callback Processing**: After authentication, WSO2 redirects back with tokens
4. **Dashboard**: Users see their profile information and database connection status
5. **Logout**: RP-Initiated Logout process that terminates both local and WSO2 sessions
