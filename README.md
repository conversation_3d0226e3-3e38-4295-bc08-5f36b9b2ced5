# FastAPI WSO2 Authentication Boilerplate

Questo progetto è un'applicazione web creata con FastAPI che dimostra l'autenticazione degli utenti tramite un server WSO2 IAM utilizzando il protocollo OAuth 2.0. Una volta autenticato, l'utente viene reindirizzato a una dashboard di benvenuto che mostra le informazioni del suo profilo e lo stato della connessione a un database PostgreSQL.

## Caratteristiche

- **Framework Web:** FastAPI
- **Autenticazione:** OAuth 2.0 con WSO2 IAM, gestita tramite la libreria `Authlib`.
- **Database:** Integrazione con PostgreSQL tramite `SQLAlchemy` per verificare lo stato della connessione.
- **Gestione Dipendenze:** `uv` come package manager.
- **Web Server:** `uvicorn`.
- **Configurazione:** Gestione delle credenziali e delle configurazioni tramite file `.env`.

## Prerequisiti

- Python 3.8+ e `uv` installato.
- Un'istanza di WSO2 Identity Server configurata con un'applicazione (Service Provider) per ottenere Client ID e Client Secret.
- Un database PostgreSQL in esecuzione e accessibile.

## Installazione

1. **Clona il repository o scarica i file.**

2. **Configura le variabili d'ambiente:**
    Crea un file `.env` nella directory principale del progetto. Puoi copiare e rinominare il file `.env.example` se fornito, o crearlo da zero. Inserisci i valori corretti:

    ```ini
    # WSO2 OAuth2 Configuration
    WSO2_CLIENT_ID="IL_TUO_CLIENT_ID_WSO2"
    WSO2_CLIENT_SECRET="IL_TUO_CLIENT_SECRET_WSO2"
    # URL di discovery OpenID Connect del tuo tenant WSO2
    WSO2_SERVER_METADATA_URL="https://server-wso2.com/oauth2/token/.well-known/openid-configuration"

    # Database Configuration (formato SQLAlchemy)
    DATABASE_URL="postgresql://utente:password@host:porta/nomedb"

    # Chiave segreta per le sessioni di Starlette/FastAPI
    # Puoi generarne una con: openssl rand -hex 32
    SECRET_KEY="UNA_CHIAVE_SEGRETA_MOLTO_SICURA"
    ```

    **IMPORTANTE:** Assicurati che l'URL di callback (`redirect_uri`) configurato nel Service Provider di WSO2 corrisponda a `http://127.0.0.1:8000/auth` per l'ambiente di sviluppo locale.

3. **Installa le dipendenze:**
    Apri un terminale nella directory del progetto ed esegui il seguente comando con `uv`:

    ```bash
    uv pip install -r requirements.txt
    ```

    O, se stai usando `pyproject.toml` direttamente:

    ```bash
    uv pip install .
    ```

    Se `pyproject.toml` non è configurato, puoi installare le dipendenze direttamente con:

    ```bash
    uv add fastapi uvicorn[standard] sqlalchemy psycopg2-binary python-dotenv requests authlib jinja2 itsdangerous httpx
    ```

## Esecuzione

Per avviare il server di sviluppo, esegui il seguente comando:

```bash
uv run uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

L'applicazione sarà disponibile all'indirizzo `http://127.0.0.1:8000`.

Per avviare il server di sviluppo con il protocollo https, esegui il seguente comando:

```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload --ssl-keyfile key.pem --ssl-certfile cert.pem
```

L'applicazione sarà disponibile all'indirizzo `https://127.0.0.1:8000`.

## Flusso dell'Applicazione

1. **Pagina di Login (`/`):** L'utente visita la pagina principale e vede un pulsante per effettuare il login.
2. **Redirect a WSO2 (`/login`):** Cliccando sul pulsante, l'utente viene reindirizzato alla pagina di login di WSO2.
3. **Callback (`/auth`):** Dopo aver inserito le credenziali su WSO2, l'utente viene reindirizzato all'applicazione. L'endpoint `/auth` gestisce la risposta, recupera il token di accesso e salva le informazioni dell'utente nella sessione.
4. **Dashboard (`/dashboard`):** L'utente viene reindirizzato alla dashboard, dove vede un messaggio di benvenuto, i dati del suo profilo (recuperati da WSO2) e lo stato attuale della connessione al database.
5. **Logout (`/logout`):** Cliccando sul pulsante di logout, viene avviato il processo di RP-Initiated Logout. L'utente viene reindirizzato a WSO2 per terminare la sessione centrale, e successivamente riportato alla pagina di login dell'applicazione. La sessione locale viene contestualmente cancellata.

## Gestione del Logout (RP-Initiated Logout)

Il processo di logout implementato in questo progetto segue le specifiche di **OpenID Connect RP-Initiated Logout**. Questo garantisce che l'utente venga disconnesso sia dall'applicazione che dal server di identità (WSO2), prevenendo accessi indesiderati.

Il flusso è il seguente:
1.  L'utente clicca sul pulsante di logout.
2.  L'applicazione recupera l'`id_token` dalla sessione utente.
3.  L'utente viene reindirizzato all'endpoint di logout di WSO2 (`/oidc/logout`).
4.  Vengono inviati due parametri fondamentali:
    *   `id_token_hint`: L'`id_token` originale, che suggerisce a WSO2 quale sessione utente terminare.
    *   `post_logout_redirect_uri`: L'URL a cui WSO2 deve reindirizzare l'utente dopo aver completato il logout (in questo caso, la homepage dell'applicazione).
5.  Contestualmente, l'applicazione cancella la propria sessione locale.

Questo approccio assicura che, al successivo tentativo di accesso, WSO2 richieda nuovamente le credenziali all'utente, garantendo una disconnessione completa e sicura.
