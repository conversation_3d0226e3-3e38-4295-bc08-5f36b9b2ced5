# FastAPI WSO2 Authentication Boilerplate

Questo progetto è un'applicazione web creata con FastAPI che dimostra l'autenticazione degli utenti tramite un server WSO2 IAM utilizzando il protocollo OAuth 2.0 / OpenID Connect. Una volta autenticato, l'utente viene reindirizzato a una dashboard di benvenuto che mostra le informazioni del suo profilo e lo stato della connessione a un database PostgreSQL.

## Caratteristiche

- **Framework Web:** FastAPI con template Jinja2
- **Autenticazione:** OAuth 2.0 / OpenID Connect con WSO2 IAM, gestita tramite la libreria `Authlib`
- **Gestione JWT:** Decodifica personalizzata dei token ID senza dipendenze esterne
- **Sessioni Ottimizzate:** Cookie di sessione ridotti per rispettare i limiti dei browser (al massimo 4096 caratteri)
- **Database:** Integrazione con PostgreSQL tramite `SQLAlchemy` per verificare lo stato della connessione
- **Gestione Dipendenze:** `uv` come package manager
- **Web Server:** `uvicorn`
- **Configurazione:** Gestione delle credenziali e delle configurazioni tramite file `.env`
- **Sicurezza:** Cookie httponly, gestione sicura delle sessioni, validazione dei token

## Prerequisiti

- Python 3.8+ e `uv` installato.
- Un'istanza di WSO2 Identity Server configurata con un'applicazione (Service Provider) per ottenere Client ID e Client Secret; si presuppone che la rotta di callback definita sia `https://localhost:8000/auth`
- Un database PostgreSQL in esecuzione e accessibile.

## Installazione

1. **Clona il repository o scarica i file.**

2. **Configura le variabili d'ambiente:**
    Crea un file `.env` nella directory principale del progetto. Puoi copiare e rinominare il file `.env.example` se fornito, o crearlo da zero. Inserisci i valori corretti:
   
   ```ini
   # WSO2 OAuth2 Configuration
   WSO2_CLIENT_ID="IL_TUO_CLIENT_ID_WSO2"
   WSO2_CLIENT_SECRET="IL_TUO_CLIENT_SECRET_WSO2"
   # URL di discovery OpenID Connect del tuo tenant WSO2
   WSO2_SERVER_METADATA_URL="https://server-wso2.com/oauth2/token/.well-known/openid-configuration"
   
   # Database Configuration (formato SQLAlchemy)
   DATABASE_URL="postgresql://utente:password@host:porta/nomedb"
   
   # Chiave segreta per le sessioni di Starlette/FastAPI
   # Puoi generarne una con: openssl rand -hex 32
   SECRET_KEY="UNA_CHIAVE_SEGRETA_MOLTO_SICURA"
   ```
   
    **IMPORTANTE:** Assicurati che l'URL di callback (`redirect_uri`) configurato nel Service Provider di WSO2 corrisponda a `http://127.0.0.1:8000/auth` per l'ambiente di sviluppo locale.

3. **Installa le dipendenze:**
    Apri un terminale nella directory del progetto ed esegui il seguente comando con `uv`:
   
   ```bash
   uv pip install -r requirements.txt
   ```
   
    O, se stai usando `pyproject.toml` direttamente:
   
   ```bash
   uv pip install .
   ```
   
    Se `pyproject.toml` non è configurato, puoi installare le dipendenze direttamente con:
   
   ```bash
   uv add fastapi uvicorn[standard] sqlalchemy psycopg2-binary python-dotenv requests authlib jinja2 itsdangerous httpx icecream
   ```

## Esecuzione

Per avviare il server di sviluppo, esegui il seguente comando:

```bash
uv run uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

L'applicazione sarà disponibile all'indirizzo `http://127.0.0.1:8000`.

### https

Per utilizzare il protocollo https bisogna fare in modo che il web server utilizzi un **certificato SSL autofirmato**.

Per generarne uno eseguire questo comando:

```bash
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -sha256 -days 365 -nodes -subj "/C=IT/ST=Sardinia/L=Cagliari/O=Dev/OU=Dev/CN=localhost"
```

Verranno generati i file `key.pem` e `cert.pem`.

In WSO2 bisognerà modificare la configurazione del Service Provider cambiando il protocollo della URL di callback da `http` a `https`.

Per avviare il web server con il certificato SSL appena creato, eseguire il seguente comando:

```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload --ssl-keyfile key.pem --ssl-certfile cert.pem
```

L'applicazione sarà ora disponibile all'indirizzo `https://127.0.0.1:8000`.

**Nota importante:** il browser mostrerà un avviso di sicurezza ("La connessione non è privata" o simile) perché **il certificato è autofirmato** e non verificato da un'autorità di certificazione. Questo è normale e sicuro per lo sviluppo locale. Procedi cliccando su "Avanzate" e poi su "Procedi su localhost (non sicuro)".

## Flusso dell'Applicazione

1. **Pagina di Login (`/`):** L'utente visita la pagina principale e vede un pulsante per effettuare il login.
2. **Redirect a WSO2 (`/login`):** Cliccando sul pulsante, l'utente viene reindirizzato alla pagina di login di WSO2.
3. **Callback (`/auth`):** Dopo aver inserito le credenziali su WSO2, l'utente viene reindirizzato all'applicazione. L'endpoint `/auth` gestisce la risposta OAuth, scambia il codice di autorizzazione con i token, decodifica l'ID token per estrarre le informazioni utente e salva i dati essenziali nella sessione.
4. **Dashboard (`/dashboard`):** L'utente viene reindirizzato alla dashboard, dove vede un messaggio di benvenuto, i dati del suo profilo (recuperati dall'ID token) e lo stato attuale della connessione al database.
5. **Logout (`/logout`):** Cliccando sul pulsante di logout, viene effettuato un logout locale. La sessione viene cancellata e l'utente viene riportato alla pagina principale.

## Architettura e Miglioramenti Implementati

### 🔧 **Gestione JWT Personalizzata**

Per risolvere problemi di compatibilità e dipendenze, è stata implementata una funzione personalizzata per la decodifica dei token JWT:

```python
def decode_jwt_payload(jwt_token):
    """Decodifica il payload di un JWT senza verificare la firma."""
    # Implementazione personalizzata usando solo librerie standard Python
    # (base64, json) senza dipendenze esterne come PyJWT
```

**Vantaggi:**

- Nessuna dipendenza esterna aggiuntiva
- Decodifica veloce e affidabile
- Sicurezza mantenuta (il token è già validato da Authlib durante lo scambio)

### 🍪 **Ottimizzazione Cookie di Sessione**

**Problema risolto:** I cookie di sessione superavano il limite di 4096 caratteri dei browser a causa dei numerosi ruoli WSO2.

**Soluzione implementata:**

- Salvataggio solo dei dati utente essenziali invece dell'intero ID token
- Conversione dei ruoli in flag booleani (`is_super_admin`, `is_sonnino_admin`)
- Riduzione da ~6000+ caratteri a ~500-800 caratteri

```python
essential_user_data = {
    'sub': user_info.get('sub'),
    'email': user_info.get('http://wso2.org/claims/emailaddress'),
    'username': user_info.get('http://wso2.org/claims/username'),
    # ... altri campi essenziali
    'is_super_admin': 'INTERNAL/IDUMANAGER_SuperAdmin' in roles,
    'is_sonnino_admin': 'G_Admins_Sonnino' in roles,
}
```

### 🔒 **Sicurezza delle Sessioni**

- **Cookie httponly:** I cookie di sessione non sono accessibili via JavaScript
- **Pulizia automatica:** Rimozione degli stati OAuth temporanei dopo l'autenticazione
- **Validazione robusta:** Controlli di null safety per tutti i client OAuth

### 🚀 **Gestione degli Errori**

- Gestione del problema del `nonce` mancante in Authlib
- Risoluzione degli errori di tipo Pylance
- Logging di debug per troubleshooting (rimovibile in produzione)

## Logout Semplificato

**Nota:** Il logout è stato semplificato per ottimizzare le prestazioni e ridurre la complessità:

- **Logout locale:** Cancellazione della sessione applicativa
- **Redirect alla homepage:** L'utente viene riportato alla pagina principale
- **Sicurezza:** Al prossimo accesso, WSO2 richiederà nuovamente l'autenticazione

Per implementare un logout completo da WSO2 (RP-Initiated Logout), sarebbe necessario salvare l'intero ID token, ma questo aumenterebbe significativamente la dimensione del cookie.

## Troubleshooting

### Cookie non visibili negli strumenti di sviluppo

**È normale!** I cookie di sessione sono configurati con `httponly=True` per sicurezza, quindi non appaiono nella tab "Application" → "Cookies" del browser. Il cookie viene comunque inviato nelle richieste HTTP.

### Errore "Cookie troppo grande"

Se dovessi riscontrare questo errore:

1. Verifica che la funzione `essential_user_data` stia filtrando correttamente i dati
2. Controlla che non ci siano ruoli WSO2 aggiuntivi molto lunghi
3. Considera di ridurre ulteriormente i dati salvati in sessione

### Problemi di autenticazione

1. **Verifica le variabili d'ambiente** nel file `.env`
2. **Controlla l'URL di callback** in WSO2 (deve essere `https://localhost:8000/auth`)
3. **Verifica i log del server** per errori specifici

## Struttura del Progetto

```text
fastapi-wso2-auth/
├── main.py                 # Applicazione principale FastAPI
├── templates/
│   ├── index.html          # Homepage con pulsante login
│   └── dashboard.html      # Dashboard utente autenticato
├── .env                    # Variabili d'ambiente (da creare)
├── .env.example           # Template per le variabili d'ambiente
├── requirements.txt       # Dipendenze Python
├── pyproject.toml        # Configurazione uv/pip
└── README.md             # Questa documentazione
```

## Dipendenze Principali

- **FastAPI**: Framework web moderno e veloce
- **Authlib**: Libreria OAuth/OpenID Connect
- **Jinja2**: Template engine per HTML
- **SQLAlchemy**: ORM per database
- **uvicorn**: Server ASGI per FastAPI
- **python-dotenv**: Gestione variabili d'ambiente
- **icecream**: Utility per debug (opzionale)

## Contributi

Questo progetto è stato sviluppato come boilerplate per l'integrazione FastAPI + WSO2. I miglioramenti implementati risolvono problemi comuni come:

- Gestione del nonce in OpenID Connect
- Ottimizzazione delle dimensioni dei cookie
- Compatibilità con i limiti dei browser
- Sicurezza delle sessioni web

Per contributi o segnalazioni, aprire una issue nel repository.
